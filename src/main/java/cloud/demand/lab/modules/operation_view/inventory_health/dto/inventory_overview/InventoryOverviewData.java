package cloud.demand.lab.modules.operation_view.inventory_health.dto.inventory_overview;


import java.math.BigDecimal;
import lombok.Data;

@Data
public class InventoryOverviewData {

    private String product;

    private Long sortNum;

    private String customhouseTitle;

    private String zoneOtherName;

    private String country;

    private String areaName;

    private String regionName;

    private String zoneName;

    private BigDecimal actualInventory;

    private BigDecimal actualMem;

    private BigDecimal actualDisk;

    private BigDecimal apiSucTotal;

    private BigDecimal apiTotal;

    private BigDecimal soldOutTotal;

    private BigDecimal soldTotal;

    private BigDecimal serviceLevel;

    private BigDecimal serviceLevelTarget;

    private BigDecimal soldScale;

    private BigDecimal totalScale;

    private BigDecimal endToEndRate;

    private BigDecimal endToEndRateTarget;

    private BigDecimal globalServiceLevelTarget;

    private BigDecimal globalEndToEndRateTarget;

}
