package cloud.demand.lab.modules.longterm.cos.service;

import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictCategoryConfigDO;
import cloud.demand.lab.modules.longterm.cos.web.dto.CosDateStorageDTO;
import cloud.demand.lab.modules.longterm.cos.web.dto.CosPurchaseStorageDTO;

import java.util.List;

/**
 * 负责查询cos底表数据的服务
 */
public interface CosDataService {

    /**
     * 获得最新的内部客户和外部客户存量数据
     * @return 返回2个是区分了内外部客户的数据
     */
    List<CosDateStorageDTO> getLatestInOutScale(CosLongtermPredictCategoryConfigDO categoryConfig);

    /**
     * 获得指定年份年初以来的累计采购量
     * @return 查询失败返回null
     */
    CosPurchaseStorageDTO getLatestYearlyAccumulatePurchase(CosLongtermPredictCategoryConfigDO categoryConfig,
                                                            Integer year);

}
