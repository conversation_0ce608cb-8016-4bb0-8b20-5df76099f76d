package cloud.demand.lab.modules.longterm.cos.web.resp;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Data
public class QueryTaskPredictResultSummaryResp {

    /**预测结果概览列表*/
    private List<PredictSummary> predictSummaryList;

    @Data
    public static class PredictSummary {

        /**策略类型*/
        private String strategyType;

        /**年份，预测概览以年为单位*/
        private Integer year;

        /**净增预测量，单位pb*/
        private BigDecimal netChangePredict;

        /**截止当前已经发生的净增量*/
        private BigDecimal netChanged;

        /**截止当前最新存量的日期*/
        private LocalDate netChangedDate;

        /**
         * 净增进度=截止当前已经发生的净增量/净增预测量
         */
        private BigDecimal netChangedProcessRate;

        /**采购预测量，单位pb*/
        private BigDecimal purchasePredict;

        /**
         * 当年度截止当前已经到货的采购量
         */
        private BigDecimal purchased;

        /**
         * 采购到货的最新日期
         */
        private LocalDate purchasedDate;

        /**
         * 采购进度=截止当前已经到货的采购量/采购预测量
         */
        private BigDecimal purchasedProcessRate;

        /**净增预测量明细*/
        private List<PredictDetail> netChangePredictDetail;

        /**
         * 已执行的净增量的明细，分内外部
         */
        private List<PredictDetail> netChangedDetail;

        /**采购预测量明细*/
        private List<PredictDetail> purchasePredictDetail;

    }

    @Data
    public static class PredictDetail {

        private String name;

        private BigDecimal value;
    }

}
