package cloud.demand.lab.modules.longterm.cos.service.impl;

import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictCategoryConfigDO;
import cloud.demand.lab.modules.longterm.cos.entity.CosLongtermPredictConfigDeviceStorageDO;
import cloud.demand.lab.modules.longterm.cos.service.CosDataService;
import cloud.demand.lab.modules.longterm.cos.web.dto.CosAccumulatePurchaseDTO;
import cloud.demand.lab.modules.longterm.cos.web.dto.CosDateStorageDTO;
import cloud.demand.lab.modules.longterm.cos.web.dto.CosPurchaseStorageDTO;
import cloud.demand.lab.modules.longterm.cos.web.dto.PlanCosScaleDataDTO;
import com.pugwoo.dbhelper.DBHelper;
import com.pugwoo.wooutils.collect.ListUtils;
import com.pugwoo.wooutils.collect.MapUtils;
import com.pugwoo.wooutils.io.IOUtils;
import com.pugwoo.wooutils.lang.DateUtils;
import lombok.SneakyThrows;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@Service
public class CosDataServiceImpl implements CosDataService {

    @Resource
    private DBHelper ckcubesDBHelper;

    @Resource
    private DBHelper cdLabDbHelper;

    @Resource
    private DBHelper planCosDBHelper;

    @Override
    @SneakyThrows
    public List<CosDateStorageDTO> getLatestInOutScale(CosLongtermPredictCategoryConfigDO categoryConfig) {
        // 1. 读取SQL文件
        String sql = IOUtils.readClasspathResourceAsString("/sql/longterm_predict/cos/plan_cos_scale.sql");
        String whereSql = categoryConfig.getWhereSql();

        // 只查询最近一周内的数据，减少数据量
        whereSql += " AND date >= " + DateUtils.format(LocalDate.now().minusDays(7), "yyyyMMdd");

        sql = sql.replace("${CONDITION}", whereSql);

        // 2. 查询历史数据
        List<PlanCosScaleDataDTO> historyData = planCosDBHelper.getRaw(PlanCosScaleDataDTO.class, sql);

        if (ListUtils.isEmpty(historyData)) {
            return ListUtils.of();
        }

        // 3. 按scope分组数据
        Map<String, List<PlanCosScaleDataDTO>> scopeGroupedData = ListUtils.toMapList(historyData,
                o -> o.getScope(), o -> o);

        // 4. 获取每个scope的最新数据
        List<CosDateStorageDTO> result = ListUtils.of();

        // 4.1 处理内部客户数据
        List<PlanCosScaleDataDTO> innerData = scopeGroupedData.get("内部");
        if (ListUtils.isNotEmpty(innerData)) {
            // 按日期排序，获取最新的数据
            ListUtils.sortDescNullLast(innerData, PlanCosScaleDataDTO::getDate);
            PlanCosScaleDataDTO latestInner = innerData.get(0);

            CosDateStorageDTO innerDto = new CosDateStorageDTO();
            innerDto.setDate(latestInner.getDate());
            innerDto.setStorage(latestInner.getValue());
            innerDto.setIsOutCustomer(false); // 内部客户
            result.add(innerDto);
        }

        // 4.2 处理外部客户数据
        List<PlanCosScaleDataDTO> outerData = scopeGroupedData.get("外部");
        if (ListUtils.isNotEmpty(outerData)) {
            // 按日期排序，获取最新的数据
            ListUtils.sortDescNullLast(outerData, PlanCosScaleDataDTO::getDate);
            PlanCosScaleDataDTO latestOuter = outerData.get(0);

            CosDateStorageDTO outerDto = new CosDateStorageDTO();
            outerDto.setDate(latestOuter.getDate());
            outerDto.setStorage(latestOuter.getValue());
            outerDto.setIsOutCustomer(true); // 外部客户
            result.add(outerDto);
        }

        return result;
    }

    @Override
    @SneakyThrows
    public CosPurchaseStorageDTO getLatestYearlyAccumulatePurchase(CosLongtermPredictCategoryConfigDO categoryConfig, Integer year) {
        // 1. 读取SQL文件
        String sql = IOUtils.readClasspathResourceAsString("/sql/longterm_predict/cos/cos_accumulate_purchase.sql");

        // 2. 查询设备存储配置并构建CASE WHEN语句
        List<CosLongtermPredictConfigDeviceStorageDO> deviceStorageList = cdLabDbHelper.getAll(
                CosLongtermPredictConfigDeviceStorageDO.class);
        String deviceStorageCaseWhen = buildDeviceStorageCaseWhen(deviceStorageList);

        // 3. 替换SQL中的占位符
        sql = sql.replace("${DEVICE_STORAGE_CASE_WHEN}", deviceStorageCaseWhen);
        sql = sql.replace("${CONDITION}", categoryConfig.getPurchaseWhereSql());

        // 4. 设置查询参数
        LocalDate startDate = LocalDate.of(year, 1, 1);
        Map<String, Object> params = MapUtils.of("startDate", startDate);

        // 6. 执行查询
        CosAccumulatePurchaseDTO result = ckcubesDBHelper.getRawOne(CosAccumulatePurchaseDTO.class, sql, params);

        // 6. 转换为返回类型
        if (result == null || result.getLatestDate() == null) {
            return null;
        }

        CosPurchaseStorageDTO response = new CosPurchaseStorageDTO();
        response.setStorage(result.getTotalPurchasePb());
        response.setDate(result.getLatestDate());
        return response;
    }

    /**
     * 构建设备存储的CASE WHEN语句
     */
    private String buildDeviceStorageCaseWhen(List<CosLongtermPredictConfigDeviceStorageDO> deviceStorageList) {
        if (ListUtils.isEmpty(deviceStorageList)) {
            return "0";
        }

        StringBuilder caseWhen = new StringBuilder("(CASE ");

        for (CosLongtermPredictConfigDeviceStorageDO deviceStorage : deviceStorageList) {
            caseWhen.append("WHEN device_type='").append(deviceStorage.getDeviceType()).append("'");

            // 如果有区域类型限制，添加区域条件
            if (deviceStorage.getAreaType() != null && !deviceStorage.getAreaType().trim().isEmpty()) {
                caseWhen.append(" AND area_type='").append(deviceStorage.getAreaType()).append("'");
            }

            caseWhen.append(" THEN ").append(deviceStorage.getStorage()).append(" ");
        }

        caseWhen.append("ELSE 0 END)");

        return caseWhen.toString();
    }

}
