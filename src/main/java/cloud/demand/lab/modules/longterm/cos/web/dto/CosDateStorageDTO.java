package cloud.demand.lab.modules.longterm.cos.web.dto;

import com.pugwoo.dbhelper.annotation.Column;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
public class CosDateStorageDTO {

    /**数据对应的日期*/
    @Column("date")
    private LocalDate date;

    /**存储量，统一单位pb*/
    @Column("storage")
    private BigDecimal storage;

    /**
     * 是否内外部客户，如果为null表示没有区分；如果不为null表示区分了
     */
    @Column("is_out_customer")
    private Boolean isOutCustomer;

}
